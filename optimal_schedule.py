#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确计算任务分配，确保三人同时结束
"""

import pandas as pd
from datetime import datetime, timedelta

def is_workday(date):
    """判断是否为工作日（周一到周五）"""
    return date.weekday() < 5

def add_workdays(start_date, work_days):
    """从开始日期添加工作日数"""
    current_date = start_date
    days_added = 0
    
    while days_added < work_days:
        if is_workday(current_date):
            days_added += 1
            if days_added < work_days:
                current_date += timedelta(days=1)
        else:
            current_date += timedelta(days=1)
    
    return current_date

def main():
    # 用例工时数据
    use_case_hours = [2.5] * 18 + [5] * 6 + [2.5] * 4 + [5] * 6 + [2.5] * 3 + [5] * 1
    total_hours = sum(use_case_hours)
    
    print(f"总工时: {total_hours} 小时")
    
    # 开始时间
    huang_start = datetime(2025, 9, 15)  # 周一
    ren_start = datetime(2025, 9, 15)    # 周一  
    zeng_start = datetime(2025, 9, 17)   # 周三
    
    # 计算到2025-09-19（周五）的可用工作日
    target_end = datetime(2025, 9, 19)
    
    # 黄子拓和任海棠：9-15到9-19，共5个工作日 = 50小时
    huang_available_days = 5
    ren_available_days = 5
    
    # 曾奇成：9-17到9-19，共3个工作日 = 30小时
    zeng_available_days = 3
    
    huang_max_hours = huang_available_days * 10  # 50小时
    ren_max_hours = ren_available_days * 10      # 50小时
    zeng_max_hours = zeng_available_days * 10    # 30小时
    
    print(f"\n可用工时:")
    print(f"黄子拓: {huang_max_hours} 小时 ({huang_available_days} 工作日)")
    print(f"任海棠: {ren_max_hours} 小时 ({ren_available_days} 工作日)")
    print(f"曾奇成: {zeng_max_hours} 小时 ({zeng_available_days} 工作日)")
    
    # 精确分配：确保总和等于127.5小时，且不超过各人最大工时
    # 黄子拓: 48.75小时, 任海棠: 48.75小时, 曾奇成: 30小时
    huang_target = 48.75
    ren_target = 48.75  
    zeng_target = 30.0
    
    print(f"\n目标分配:")
    print(f"黄子拓: {huang_target} 小时")
    print(f"任海棠: {ren_target} 小时")
    print(f"曾奇成: {zeng_target} 小时")
    print(f"总计: {huang_target + ren_target + zeng_target} 小时")
    
    # 手动精确分配用例
    # 5小时用例：6个，2.5小时用例：32个
    # 总计：6*5 + 32*2.5 = 30 + 80 = 110小时... 等等，让我重新计算
    
    print(f"\n用例分布:")
    hours_5 = [i+1 for i, h in enumerate(use_case_hours) if h == 5.0]
    hours_2_5 = [i+1 for i, h in enumerate(use_case_hours) if h == 2.5]
    
    print(f"5小时用例: {len(hours_5)}个 - {hours_5}")
    print(f"2.5小时用例: {len(hours_2_5)}个 - {hours_2_5}")
    print(f"验证总工时: {len(hours_5)*5 + len(hours_2_5)*2.5} 小时")
    
    # 精确分配策略：
    # 黄子拓: 48.75小时 = 9个5小时用例(45小时) + 1.5个2.5小时用例(3.75小时) = 9*5 + 1.5*2.5 = 48.75
    # 但1.5个用例不现实，所以：9个5小时 + 2个2.5小时 = 50小时（稍微超出）
    # 调整为：8个5小时 + 3.5个2.5小时 = 40 + 8.75 = 48.75（3.5个不现实）
    # 再调整：8个5小时 + 4个2.5小时 = 40 + 10 = 50小时
    # 最终调整：7个5小时 + 7个2.5小时 = 35 + 17.5 = 52.5小时（超出）
    # 最优：6个5小时 + 7个2.5小时 = 30 + 17.5 = 47.5小时
    
    # 重新精确分配
    assignments = {
        '黄子拓': {
            '5h_cases': hours_5[:6],      # 6个5小时用例 = 30小时
            '2.5h_cases': hours_2_5[:7],  # 7个2.5小时用例 = 17.5小时
            'total': 47.5
        },
        '任海棠': {
            '5h_cases': hours_5[6:12],    # 6个5小时用例 = 30小时  
            '2.5h_cases': hours_2_5[7:14], # 7个2.5小时用例 = 17.5小时
            'total': 47.5
        },
        '曾奇成': {
            '5h_cases': [],               # 0个5小时用例 = 0小时
            '2.5h_cases': hours_2_5[14:], # 剩余2.5小时用例
            'total': len(hours_2_5[14:]) * 2.5
        }
    }
    
    # 验证分配
    print(f"\n分配验证:")
    total_assigned = 0
    for person, data in assignments.items():
        person_total = len(data['5h_cases']) * 5 + len(data['2.5h_cases']) * 2.5
        total_assigned += person_total
        print(f"{person}: {len(data['5h_cases'])}个5h用例 + {len(data['2.5h_cases'])}个2.5h用例 = {person_total}小时")
    
    print(f"总分配工时: {total_assigned}小时")
    
    # 如果曾奇成工时不够30小时，从其他人转移一些2.5小时用例
    if assignments['曾奇成']['total'] < 30:
        needed = 30 - assignments['曾奇成']['total']
        cases_needed = int(needed / 2.5)
        print(f"\n曾奇成需要额外 {needed} 小时，即 {cases_needed} 个2.5小时用例")
        
        # 从任海棠转移用例给曾奇成
        if cases_needed <= len(assignments['任海棠']['2.5h_cases']):
            transfer_cases = assignments['任海棠']['2.5h_cases'][-cases_needed:]
            assignments['任海棠']['2.5h_cases'] = assignments['任海棠']['2.5h_cases'][:-cases_needed]
            assignments['曾奇成']['2.5h_cases'].extend(transfer_cases)
            
            # 重新计算
            assignments['任海棠']['total'] = len(assignments['任海棠']['5h_cases']) * 5 + len(assignments['任海棠']['2.5h_cases']) * 2.5
            assignments['曾奇成']['total'] = len(assignments['曾奇成']['2.5h_cases']) * 2.5
    
    # 生成最终任务表
    task_list = []
    
    for person, data in assignments.items():
        all_cases = data['5h_cases'] + data['2.5h_cases']
        all_cases.sort()
        
        for case_id in all_cases:
            case_hours = use_case_hours[case_id - 1]
            task_list.append({
                '用例编号': f'用例{case_id:02d}',
                '用例工时': case_hours,
                '责任人': person,
                '开始时间': start_dates[person].strftime('%Y-%m-%d') if person in ['黄子拓', '任海棠'] else '2025-09-17',
                '结束时间': '2025-09-19'  # 目标统一结束时间
            })
    
    start_dates = {
        '黄子拓': '2025-09-15',
        '任海棠': '2025-09-15', 
        '曾奇成': '2025-09-17'
    }
    
    # 按用例编号排序
    task_list.sort(key=lambda x: int(x['用例编号'][2:]))
    
    # 创建DataFrame并保存
    df = pd.DataFrame(task_list)
    df.to_csv('final_task_allocation.csv', index=False, encoding='utf-8-sig')
    
    print(f"\n最终分配结果:")
    print("=" * 60)
    for person in ['黄子拓', '任海棠', '曾奇成']:
        person_tasks = df[df['责任人'] == person]
        total_hours = person_tasks['用例工时'].sum()
        task_count = len(person_tasks)
        
        print(f"\n{person}:")
        print(f"  任务数量: {task_count} 个")
        print(f"  总工时: {total_hours} 小时")
        print(f"  开始时间: {start_dates[person]}")
        print(f"  结束时间: 2025-09-19")
        print(f"  用例列表: {sorted([int(x[2:]) for x in person_tasks['用例编号']])}")
    
    print(f"\n文件已保存为: final_task_allocation.csv")

if __name__ == "__main__":
    main()
