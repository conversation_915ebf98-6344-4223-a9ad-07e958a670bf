// ==UserScript==
// @name         Amazon 评论采集器 (Review Scraper) Enhanced
// @namespace    https://yourdomain.example
// @version      1.3.1
// @description  采集亚马逊商品评论（用户名、星级、标题、内容等），支持自动翻-页并导出 CSV / JSON。已优化对动态翻页（SPA）的支持。请遵守亚马逊条款与当地法律！
// <AUTHOR> (Modified by Gemini)
// @include      *amazon*
// @exclude      *amazon*checkout*
// @exclude      *amazon*cart*
// @exclude      *amazon*account*
// @icon         https://www.amazon.com/favicon.ico
// @grant        GM_setClipboard
// @grant        GM_setValue
// @grant        GM_getValue
// @license      MIT
// ==/UserScript==

(function () {
  'use strict';

  // 检查当前页面是否为评论页面
  function isReviewPage() {
    const url = location.href;
    const isReviewURL = url.includes('product-reviews') ||
                       url.includes('/reviews/') ||
                       url.includes('customer-reviews');

    const hasReviewElements = document.querySelector('[data-hook="review"]') ||
                             document.querySelector('.review') ||
                             document.querySelector('[id*="customer_review"]') ||
                             document.querySelector('.a-section.review');

    return isReviewURL || hasReviewElements;
  }

  // 如果不是评论页面，不加载脚本
  if (!isReviewPage()) {
    console.log('🚫 当前页面不是Amazon评论页面，脚本未加载');
    return;
  }

  console.log('✅ 检测到Amazon评论页面，加载采集器...');

  /**************** 配置区域（可按需调整） ****************/
  const CONFIG = {
    maxAutoPages: 20,           // 自动模式最大页数（从当前页开始计）
    delayBase: 2000,            // 翻页基础延迟(ms) - 增加延迟避免被检测
    delayJitter: 1500,          // 额外随机抖动(ms)
    logLimit: 1200,            // 面板日志最大行数
    parseDateToISO: true,       // 尝试将日期文本解析为 ISO8601
    maxRetries: 3,             // 最大重试次数
    stopOnCaptchaKeywords: ['automated access', 'captcha', 'unusual traffic', 'verify you are human', 'enter the characters you see below'],
  };

  /**************** 状态变量 ****************/
  let collected = [];           // 已采集评论对象数组
  let collectedIds = new Set(); // 去重集合
  let autoMode = false;         // 是否正在自动翻页中
  let currentPage = getQueryParam('pageNumber') ? parseInt(getQueryParam('pageNumber'), 10) : 1;
  let totalTriedPages = 0;
  let retryCount = 0;

  /**************** UI 创建 ****************/
  const panel = document.createElement('div');
  panel.style.cssText = `
    position:fixed; z-index:999999; right:12px; bottom:12px; width:340px;
    background:#1e2327; color:#fff; font-size:12px; font-family:Arial, sans-serif;
    border:1px solid #444; border-radius:8px; box-shadow:0 4px 16px rgba(0,0,0,.5); padding:12px;
    backdrop-filter: blur(10px);
  `;
  panel.innerHTML = `
    <div style="font-weight:bold; font-size:14px; margin-bottom:8px; color:#4CAF50;">
      🛒 Amazon 评论采集器 v1.3.1
    </div>
    <div style="font-size:11px; color:#aaa; margin-bottom:8px;">
      已采集: <span id="rc-count">0</span> 条 | 当前页: <span id="rc-page">${currentPage}</span>
      <button id="rc-btn-refresh-page" style="margin-left:8px; padding:2px 6px; border:none; border-radius:3px; background:#666; color:white; cursor:pointer; font-size:10px;">🔄</button>
    </div>
    <div style="display:flex; flex-wrap:wrap; gap:6px; margin-bottom:8px;">
      <button id="rc-btn-collect" style="flex:1; padding:6px; border:none; border-radius:4px; background:#2196F3; color:white; cursor:pointer;">采集当前页</button>
      <button id="rc-btn-auto" style="flex:1; padding:6px; border:none; border-radius:4px; background:#4CAF50; color:white; cursor:pointer;">自动采集</button>
      <button id="rc-btn-stop" style="flex:1; padding:6px; border:none; border-radius:4px; background:#f44336; color:white; cursor:pointer;">停止</button>
    </div>
    <div style="display:flex; gap:6px; margin-bottom:8px;">
      <button id="rc-btn-resume" style="flex:1; padding:6px; border:none; border-radius:4px; background:#FF5722; color:white; cursor:pointer; font-size:11px;">强制恢复</button>
      <button id="rc-btn-debug" style="flex:1; padding:6px; border:none; border-radius:4px; background:#795548; color:white; cursor:pointer; font-size:11px;">调试状态</button>
    </div>
    <div style="display:flex; gap:6px; margin-bottom:8px;">
      <button id="rc-btn-json" style="flex:1; padding:6px; border:none; border-radius:4px; background:#FF9800; color:white; cursor:pointer;">导出JSON</button>
      <button id="rc-btn-csv" style="flex:1; padding:6px; border:none; border-radius:4px; background:#9C27B0; color:white; cursor:pointer;">导出CSV</button>
      <button id="rc-btn-clear" style="flex:1; padding:6px; border:none; border-radius:4px; background:#607D8B; color:white; cursor:pointer;">清空</button>
    </div>
    <div style="font-size:11px; line-height:1.4; max-height:150px; overflow:auto; background:#111; padding:8px; border:1px solid #333; border-radius:4px;" id="rc-log"></div>
  `;
  document.body.appendChild(panel);

  // 获取UI元素
  const btnCollect = panel.querySelector('#rc-btn-collect');
  const btnAuto = panel.querySelector('#rc-btn-auto');
  const btnStop = panel.querySelector('#rc-btn-stop');
  const btnResume = panel.querySelector('#rc-btn-resume');
  const btnDebug = panel.querySelector('#rc-btn-debug');
  const btnJson = panel.querySelector('#rc-btn-json');
  const btnCsv = panel.querySelector('#rc-btn-csv');
  const btnClear = panel.querySelector('#rc-btn-clear');
  const btnRefreshPage = panel.querySelector('#rc-btn-refresh-page');
  const logBox = panel.querySelector('#rc-log');
  const countSpan = panel.querySelector('#rc-count');
  const pageSpan = panel.querySelector('#rc-page');

  /**************** 事件绑定 ****************/
  btnCollect.addEventListener('click', () => {
    autoMode = false;
    log(`开始采集第 ${currentPage} 页（手动模式）...`);
    collectCurrentPageWithRetry();
  });

  btnAuto.addEventListener('click', () => {
    if (autoMode) {
      log('⚠️ 已经在自动模式中，当前状态：');
      log(`   - autoMode: ${autoMode}`);
      log(`   - totalTriedPages: ${totalTriedPages}`);
      log(`   - currentPage: ${currentPage}`);
      log(`   - localStorage状态: ${localStorage.getItem('amazonReviewScraperAutoMode')}`);
      return;
    }
    autoMode = true;
    totalTriedPages = 0;
    retryCount = 0;
    // 清除可能残留的localStorage状态
    clearAutoModeState();
    log(`🚀 自动采集启动。从第 ${currentPage} 页开始，最大页数限制：${CONFIG.maxAutoPages}`);
    autoLoop();
  });

  btnStop.addEventListener('click', () => {
    autoMode = false;
    clearAutoModeState(); // 清除localStorage状态
    log('⏹️ 已请求停止，并清除自动模式状态。');
  });

  btnRefreshPage.addEventListener('click', () => {
    updateCurrentPage();
    log('🔄 页码已刷新');
  });

  btnResume.addEventListener('click', () => {
    log('🔧 强制恢复自动模式...');
    checkAutoModeResume();
  });

  btnDebug.addEventListener('click', () => {
    log('🔍 调试状态信息：');
    log(`   - autoMode: ${autoMode}`);
    log(`   - totalTriedPages: ${totalTriedPages}`);
    log(`   - currentPage: ${currentPage}`);
    log(`   - collected.length: ${collected.length}`);

    const shouldResume = localStorage.getItem('amazonReviewScraperAutoMode');
    const savedTotalPages = localStorage.getItem('amazonReviewScraperTotalPages');
    const savedMaxPages = localStorage.getItem('amazonReviewScraperMaxPages');
    const clickTime = localStorage.getItem('amazonReviewScraperClickTime');

    log(`   - localStorage.shouldResume: ${shouldResume}`);
    log(`   - localStorage.savedTotalPages: ${savedTotalPages}`);
    log(`   - localStorage.savedMaxPages: ${savedMaxPages}`);
    log(`   - localStorage.clickTime: ${clickTime}`);

    if (clickTime) {
      const timeDiff = Date.now() - parseInt(clickTime);
      log(`   - 距离上次点击: ${Math.round(timeDiff/1000)}秒`);
    }

    const nextButton = findNextPageButton();
    log(`   - 下一页按钮: ${nextButton ? '找到' : '未找到'}`);
    if (nextButton) {
      log(`   - 下一页URL: ${nextButton.href}`);
    }

    const reviewItems = getReviewItems();
    log(`   - 当前页评论数: ${reviewItems.length}`);
  });

  btnJson.addEventListener('click', () => {
    if (!collected.length) {
      log('❌ 当前没有数据可导出。');
      return;
    }
    const jsonStr = JSON.stringify(collected, null, 2);
    downloadFile(jsonStr, `amazon_reviews_${getTimestamp()}_${collected.length}.json`, 'application/json;charset=utf-8;');
  });

  btnCsv.addEventListener('click', () => {
    if (!collected.length) {
      log('❌ 当前没有数据可导出。');
      return;
    }
    const csvStr = toCSV(collected);
    const bom = "\uFEFF"; // BOM for Excel UTF-8 support
    downloadFile(bom + csvStr, `amazon_reviews_${getTimestamp()}_${collected.length}.csv`, 'text/csv;charset=utf-8;');
  });

  btnClear.addEventListener('click', () => {
    if (confirm('确定要清空所有已采集的数据吗？')) {
      collected = [];
      collectedIds.clear();
      updateUI();
      log('🗑️ 数据已清空。');
    }
  });

  /**************** 核心函数：采集页面 ****************/
  function collectCurrentPageWithRetry() {
    try {
      collectCurrentPage();
      retryCount = 0; // 重置重试计数
    } catch (error) {
      log(`❌ 采集出错: ${error.message}`);
      retryCount++;

      if (retryCount <= CONFIG.maxRetries) {
        log(`🔄 第 ${retryCount} 次重试...`);
        setTimeout(() => collectCurrentPageWithRetry(), 2000);
      } else {
        log('❌ 重试次数已达上限，停止采集');
        autoMode = false;
        retryCount = 0;
      }
    }
  }

  function collectCurrentPage() {
    // 更新当前页码
    updateCurrentPage();

    // 检测页面是否包含验证码或访问限制提示
    if (detectCaptcha()) {
      log('🚫 疑似触发亚马逊访问验证/机器人检测，已停止。');
      autoMode = false;
      return;
    }

    const reviewItems = getReviewItems();
    if (!reviewItems.length) {
      log('⚠️ 未检测到评论节点，可能页面结构已变或没有评论。');
      // 调试模式：显示页面中可能的评论容器
      debugPageStructure();
      return;
    }

    let countNew = 0;
    reviewItems.forEach(block => {
      const reviewData = extractReviewData(block);
      if (reviewData && !collectedIds.has(reviewData.reviewId)) {
        collected.push(reviewData);
        collectedIds.add(reviewData.reviewId);
        countNew++;
      }
    });

    updateUI();
    log(`✅ 第 ${currentPage} 页采集完成：新增 ${countNew} 条，总计 ${collected.length} 条。`);
  }

  // 更健壮的评论项获取
  function getReviewItems() {
    const selectors = [
      'div[data-hook="review"]',
      'div[id*="customer_review"]',
      'div.a-section.celwidget[id*="customer_review"]',
      '[data-testid="reviews-section"] > div',
      '.review-item',
      '.a-section.review'
    ];

    for (const selector of selectors) {
      const items = document.querySelectorAll(selector);
      if (items.length > 0) {
        return items;
      }
    }
    return [];
  }

  // 提取评论数据
  function extractReviewData(block) {
    const reviewId = block.getAttribute('id') || block.getAttribute('data-review-id') || '';
    if (!reviewId) return null;

    const profileName = text(block.querySelector('.a-profile-name')) ||
                       text(block.querySelector('.a-profile-content .a-profile-name')) ||
                       text(block.querySelector('[data-hook="review-author"]'));

    const starData = getStarRating(block);

    const title = text(block.querySelector('[data-hook="review-title"] span:not(.a-color-base)')) ||
                  text(block.querySelector('[data-hook="review-title"] span')) ||
                  text(block.querySelector('.review-title-content span')) ||
                  text(block.querySelector('.review-title'));

    const body = getReviewBody(block);

    const dateRaw = text(block.querySelector('[data-hook="review-date"]')) ||
                   text(block.querySelector('.review-date'));
    const dateISO = CONFIG.parseDateToISO ? tryParseDate(dateRaw) : '';

    const verified = !!block.querySelector('[data-hook="avp-badge"]') ||
                    !!block.querySelector('.a-color-success');

    const imgCount = block.querySelectorAll('.review-image-tile-section img, .review-image img').length;

    const helpfulRaw = text(block.querySelector('[data-hook="helpful-vote-statement"]'));
    const helpful = parseHelpful(helpfulRaw);

    return {
      reviewId,
      reviewer: profileName,
      star: starData.star,
      starText: starData.starText,
      title,
      dateRaw,
      dateISO,
      body,
      verified,
      imageCount: imgCount,
      helpful,
      pageNumber: currentPage,
      url: location.href,
      scrapedAt: new Date().toISOString()
    };
  }

  function getStarRating(block) {
    const starSelectors = [
      'i[data-hook="cmps-review-star-rating"] span.a-icon-alt',
      'i[data-hook="review-star-rating"] span.a-icon-alt',
      '.a-icon-star span.a-icon-alt',
      '[class*="star"] span'
    ];

    for (const selector of starSelectors) {
      const starEl = block.querySelector(selector);
      if (starEl) {
        const starText = starEl.textContent.trim();
        const match = starText.match(/(\d+(?:\.\d+)?)/);
        if (match) {
          return { star: parseFloat(match[1]), starText: starText };
        }
      }
    }
    return { star: null, starText: '' };
  }

  function getReviewBody(block) {
    const bodySelectors = [
      '[data-hook="review-body"]', '.review-text',
      '.review-text-content', '.cr-original-review-text'
    ];
    for (const selector of bodySelectors) {
      const bodyNode = block.querySelector(selector);
      if (bodyNode) {
        const spans = bodyNode.querySelectorAll('span');
        if (spans.length > 0) {
          return [...new Set(Array.from(spans).map(s => s.textContent.trim()).filter(Boolean))].join('\n');
        }
        return bodyNode.textContent.trim();
      }
    }
    return '';
  }

  function debugPageStructure() {
    log('🔍 调试模式：分析页面结构...');
    const possibleContainers = [
      'div[id*="review"]', 'div[id*="customer"]', 'div.a-section',
      'div[data-hook*="review"]', 'div.celwidget'
    ];
    possibleContainers.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        log(`📍 找到 ${elements.length} 个 "${selector}" 元素`);
      }
    });
  }

  function updateCurrentPage() {
    const urlPageNumber = getQueryParam('pageNumber');
    const newPage = urlPageNumber ? parseInt(urlPageNumber, 10) : 1;
    if (newPage !== currentPage) {
      currentPage = newPage;
      log(`📄 页码已更新为: ${currentPage}`);
    }
    updateUI();
  }

  /**************** 自动翻页循环 ****************/
  function autoLoop() {
    if (!autoMode) {
      log('⏹️ 自动模式已停止。');
      return;
    }
    log(`🔄 开始自动循环 - 当前页: ${currentPage}, 已处理页数: ${totalTriedPages}/${CONFIG.maxAutoPages}`);
    collectCurrentPageWithRetry();
    totalTriedPages++;
    if (totalTriedPages >= CONFIG.maxAutoPages) {
      log(`🏁 达到最大自动页数 ${CONFIG.maxAutoPages}，停止。`);
      autoMode = false;
      clearAutoModeState();
      return;
    }
    const nextButton = findNextPageButton();
    if (!nextButton) {
      log('🏁 未找到下一页按钮，自动采集结束。');
      autoMode = false;
      clearAutoModeState();
      return;
    }
    const delay = CONFIG.delayBase + Math.random() * CONFIG.delayJitter;
    log(`⏭️ 准备点击下一页按钮，延迟 ${Math.round(delay)} ms ...`);
    setTimeout(() => {
      if (!autoMode) {
        log('⏹️ 翻页前检测到停止信号，中止。');
        return;
      }
      localStorage.setItem('amazonReviewScraperAutoMode', 'true');
      localStorage.setItem('amazonReviewScraperTotalPages', totalTriedPages.toString());
      localStorage.setItem('amazonReviewScraperMaxPages', CONFIG.maxAutoPages.toString());
      localStorage.setItem('amazonReviewScraperClickTime', Date.now().toString());
      log('🖱️ 模拟点击下一页按钮...');
      try {
        nextButton.click();
        log('✅ 下一页按钮点击成功，等待页面跳转或内容更新...');
      } catch (error) {
        log(`❌ 点击下一页按钮失败: ${error.message}`);
        autoMode = false;
        clearAutoModeState();
      }
    }, delay);
  }

  /**************** 工具函数 ****************/
  function text(el) { return el ? el.textContent.trim() : ''; }
  function parseHelpful(raw) {
    if (!raw) return 0;
    const digits = raw.match(/([\d,\.]+)/);
    if (digits) return parseInt(digits[1].replace(/[,\.]/g, ''), 10) || 0;
    if (/\bOne\b/i.test(raw)) return 1;
    return 0;
  }
  function tryParseDate(dateStr) {
    if (!dateStr) return '';
    const onIdx = dateStr.toLowerCase().lastIndexOf(' on ');
    const candidate = (onIdx !== -1) ? dateStr.slice(onIdx + 4).trim() : dateStr;
    const d = new Date(candidate);
    return !isNaN(d.getTime()) ? d.toISOString() : '';
  }
  function getQueryParam(key) {
    return new URL(location.href).searchParams.get(key);
  }
  function findNextPageButton() {
    if (isLastPage()) return null;
    const selectors = [
      'li.a-last a:not(.a-disabled)',
      '.a-pagination .a-last a:not(.a-disabled)',
    ];
    for (const selector of selectors) {
        const button = document.querySelector(selector);
        if (button && button.href) {
            log(`✅ 找到下一页按钮: ${selector}`);
            return button;
        }
    }
    log('❌ 未找到有效的下一页按钮');
    return null;
  }
  function isLastPage() {
    return document.querySelector('li.a-last.a-disabled, .a-pagination .a-last.a-disabled') != null;
  }
  function detectCaptcha() {
    const bodyText = document.body.innerText.toLowerCase();
    for (const keyword of CONFIG.stopOnCaptchaKeywords) {
      if (bodyText.includes(keyword)) {
        log(`🚫 检测到反爬虫标识: ${keyword}`);
        return true;
      }
    }
    return false;
  }
  function updateUI() {
    countSpan.textContent = collected.length;
    pageSpan.textContent = currentPage;
  }
  function getTimestamp() {
    return new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  }
  function downloadFile(dataStr, filename, mime) {
    const blob = new Blob([dataStr], { type: mime });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    a.remove();
    log(`📥 已触发下载：${filename}`);
  }
  function escapeCSVField(field) {
    if (field == null) return '';
    let s = String(field).replace(/\r?\n/g, ' ');
    if (/[",]/.test(s)) s = '"' + s.replace(/"/g, '""') + '"';
    return s;
  }
  function toCSV(arr) {
    const headers = ['reviewId', 'reviewer', 'star', 'starText', 'title', 'dateRaw', 'dateISO', 'body', 'verified', 'imageCount', 'helpful', 'pageNumber', 'url', 'scrapedAt'];
    const lines = [headers.join(',')];
    arr.forEach(o => lines.push(headers.map(h => escapeCSVField(o[h])).join(',')));
    return lines.join('\n');
  }
  function log(msg) {
    const time = new Date().toLocaleTimeString();
    const line = `[${time}] ${msg}`;
    const div = document.createElement('div');
    div.textContent = line;
    div.style.cssText = 'margin-bottom: 2px; padding: 2px 0; border-bottom: 1px solid #333;';
    logBox.appendChild(div);
    if (logBox.children.length > CONFIG.logLimit) logBox.removeChild(logBox.firstChild);
    logBox.scrollTop = logBox.scrollHeight;
    console.log('[ReviewScraper]', msg);
  }

  /**************** 数据持久化 ****************/
  function initializeData() {
    try {
      const savedData = GM_getValue('collected_reviews', '[]');
      if (savedData && savedData !== '[]') {
        collected = JSON.parse(savedData);
        collectedIds = new Set(collected.map(r => r.reviewId));
        log(`📂 从本地存储恢复了 ${collected.length} 条评论数据`);
      }
    } catch (e) { log(`⚠️ 恢复数据失败: ${e.message}`); }
  }
  function saveData() {
    try {
      GM_setValue('collected_reviews', JSON.stringify(collected));
    } catch (e) { log(`⚠️ 保存数据失败: ${e.message}`); }
  }
  setInterval(saveData, 30000);
  window.addEventListener('beforeunload', saveData);

  /**************** 自动模式恢复与页面就绪 ****************/
  function checkAutoModeResume() {
    if (autoMode) {
      log('🔍 自动模式已在运行中，跳过恢复检查');
      return;
    }
    const shouldResume = localStorage.getItem('amazonReviewScraperAutoMode');
    if (shouldResume !== 'true') return;

    log('🔍 检查自动模式恢复状态...');
    const savedTotalPages = parseInt(localStorage.getItem('amazonReviewScraperTotalPages') || '0', 10);
    const clickTime = parseInt(localStorage.getItem('amazonReviewScraperClickTime') || '0', 10);
    if (clickTime && (Date.now() - clickTime) > 120000) {
      log('⚠️ 检测到可能的翻页超时，清除状态');
      clearAutoModeState();
      return;
    }
    autoMode = true;
    totalTriedPages = savedTotalPages;
    log(`🔄 恢复自动采集模式，已处理 ${totalTriedPages} 页`);
    waitForPageReady(() => {
      if (autoMode) {
        log('▶️ 页面已就绪，开始恢复的自动采集循环...');
        clearAutoModeState();
        autoLoop();
      }
    });
  }
  function waitForPageReady(callback) {
    let attempts = 0;
    const maxAttempts = 20;
    const checkReady = () => {
      if (getReviewItems().length > 0) {
        log(`✅ 页面已就绪 (尝试 ${attempts + 1} 次)`);
        callback();
      } else if (++attempts >= maxAttempts) {
        log('⚠️ 页面就绪检查超时，但仍尝试开始');
        callback();
      } else {
        setTimeout(checkReady, 500);
      }
    };
    checkReady();
  }
  function clearAutoModeState() {
    localStorage.removeItem('amazonReviewScraperAutoMode');
    localStorage.removeItem('amazonReviewScraperTotalPages');
    localStorage.removeItem('amazonReviewScraperMaxPages');
    localStorage.removeItem('amazonReviewScraperClickTime');
    log('🧹 已清除localStorage中的自动模式状态');
  }

  /**************** 启动与页面变化监听 (核心修正 v2) ****************/

  let currentHref = location.href;

  // 页面首次加载时运行的函数
  function onInitialLoad() {
    initializeData();
    updateCurrentPage();
    // 检查是否需要从一个完整的页面刷新中恢复任务
    checkAutoModeResume();
  }

  // SPA导航后调用的函数
  function onSpaNavigation() {
    log(`🔀 检测到页面导航 (SPA)`);
    updateCurrentPage();

    // 关键修正：如果autoMode已为true，说明是正在进行的任务，直接继续循环
    if (autoMode) {
        log('▶️ 自动模式运行中，为新页面继续循环...');
        waitForPageReady(() => {
            if (autoMode) { // 再次检查以防用户中途点击了停止
                autoLoop();
            } else {
                log('⏹️ 在等待页面就绪时，自动模式被停止');
            }
        });
    }
  }

  // 使用 MutationObserver 来监听DOM变化，捕获SPA导航
  const observer = new MutationObserver(() => {
    if (location.href !== currentHref) {
      currentHref = location.href;
      onSpaNavigation();
    }
  });

  // 脚本开始执行
  onInitialLoad();
  if (document.body) {
    observer.observe(document.body, { childList: true, subtree: true });
  }

  log(`🚀 脚本已加载。随时可开始采集。`);
})();