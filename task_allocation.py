#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务分配计算器
计算三个责任人的任务分配，使结束时间大约相同
"""

from datetime import datetime, timedelta
import pandas as pd

def is_workday(date):
    """判断是否为工作日（周一到周五）"""
    return date.weekday() < 5

def add_workdays(start_date, hours, hours_per_day=10):
    """从开始日期添加工作小时数，返回结束日期"""
    current_date = start_date
    remaining_hours = hours
    
    while remaining_hours > 0:
        if is_workday(current_date):
            if remaining_hours >= hours_per_day:
                remaining_hours -= hours_per_day
                if remaining_hours > 0:
                    current_date += timedelta(days=1)
            else:
                # 最后一天，不足一整天
                current_date = current_date
                remaining_hours = 0
        else:
            current_date += timedelta(days=1)
    
    return current_date

def calculate_workdays_between(start_date, end_date):
    """计算两个日期之间的工作日数"""
    current_date = start_date
    workdays = 0
    
    while current_date <= end_date:
        if is_workday(current_date):
            workdays += 1
        current_date += timedelta(days=1)
    
    return workdays

def main():
    # 用例工时数据
    use_case_hours = [2.5] * 18 + [5] * 6 + [2.5] * 4 + [5] * 6 + [2.5] * 3 + [5] * 1
    total_hours = sum(use_case_hours)
    
    print(f"总工时: {total_hours} 小时")
    print(f"用例数量: {len(use_case_hours)} 个")
    
    # 责任人信息
    huang_start = datetime(2025, 9, 15)  # 黄子拓开始时间
    ren_start = datetime(2025, 9, 15)    # 任海棠开始时间
    zeng_start = datetime(2025, 9, 17)   # 曾奇成开始时间
    
    print(f"\n开始时间:")
    print(f"黄子拓: {huang_start.strftime('%Y-%m-%d')}")
    print(f"任海棠: {ren_start.strftime('%Y-%m-%d')}")
    print(f"曾奇成: {zeng_start.strftime('%Y-%m-%d')}")
    
    # 计算曾奇成相对于其他两人的延迟工作日
    zeng_delay_days = calculate_workdays_between(huang_start, zeng_start - timedelta(days=1))
    zeng_delay_hours = zeng_delay_days * 10
    
    print(f"\n曾奇成延迟 {zeng_delay_days} 个工作日 ({zeng_delay_hours} 小时)")
    
    # 为了使三人同时结束，需要重新分配工时
    # 设黄子拓和任海棠的工时为 x，曾奇成的工时为 y
    # 约束条件：x + x + y = total_hours
    # 时间约束：x/10 = (y + zeng_delay_hours)/10
    # 即：x = y + zeng_delay_hours
    
    # 解方程：2x + y = total_hours, x = y + zeng_delay_hours
    # 代入得：2(y + zeng_delay_hours) + y = total_hours
    # 3y + 2*zeng_delay_hours = total_hours
    # y = (total_hours - 2*zeng_delay_hours) / 3
    
    zeng_hours = (total_hours - 2 * zeng_delay_hours) / 3
    huang_hours = zeng_hours + zeng_delay_hours
    ren_hours = huang_hours  # 任海棠和黄子拓工时相同
    
    print(f"\n优化后的工时分配:")
    print(f"黄子拓: {huang_hours:.1f} 小时")
    print(f"任海棠: {ren_hours:.1f} 小时") 
    print(f"曾奇成: {zeng_hours:.1f} 小时")
    print(f"总计: {huang_hours + ren_hours + zeng_hours:.1f} 小时")
    
    # 计算结束时间
    huang_end = add_workdays(huang_start, huang_hours)
    ren_end = add_workdays(ren_start, ren_hours)
    zeng_end = add_workdays(zeng_start, zeng_hours)
    
    print(f"\n结束时间:")
    print(f"黄子拓: {huang_end.strftime('%Y-%m-%d')}")
    print(f"任海棠: {ren_end.strftime('%Y-%m-%d')}")
    print(f"曾奇成: {zeng_end.strftime('%Y-%m-%d')}")
    
    # 按工时分配用例
    print(f"\n用例分配建议:")
    
    # 将用例按工时排序，优先分配大工时用例
    sorted_cases = sorted(enumerate(use_case_hours, 1), key=lambda x: x[1], reverse=True)
    
    # 初始化分配
    assignments = {
        '黄子拓': {'cases': [], 'total_hours': 0},
        '任海棠': {'cases': [], 'total_hours': 0},
        '曾奇成': {'cases': [], 'total_hours': 0}
    }
    
    target_hours = {'黄子拓': huang_hours, '任海棠': ren_hours, '曾奇成': zeng_hours}
    
    # 贪心算法分配用例
    for case_id, hours in sorted_cases:
        # 找到当前工时最少且还能容纳该用例的责任人
        best_person = None
        min_diff = float('inf')
        
        for person in assignments:
            current_hours = assignments[person]['total_hours']
            if current_hours + hours <= target_hours[person]:
                diff = target_hours[person] - (current_hours + hours)
                if diff < min_diff:
                    min_diff = diff
                    best_person = person
        
        if best_person:
            assignments[best_person]['cases'].append(case_id)
            assignments[best_person]['total_hours'] += hours
        else:
            # 如果没有完全匹配的，分配给工时最少的人
            best_person = min(assignments.keys(), 
                            key=lambda x: assignments[x]['total_hours'])
            assignments[best_person]['cases'].append(case_id)
            assignments[best_person]['total_hours'] += hours
    
    for person, data in assignments.items():
        print(f"\n{person}:")
        print(f"  分配用例: {sorted(data['cases'])}")
        print(f"  用例数量: {len(data['cases'])} 个")
        print(f"  总工时: {data['total_hours']:.1f} 小时")
        print(f"  目标工时: {target_hours[person]:.1f} 小时")
        print(f"  差异: {abs(data['total_hours'] - target_hours[person]):.1f} 小时")

if __name__ == "__main__":
    main()
