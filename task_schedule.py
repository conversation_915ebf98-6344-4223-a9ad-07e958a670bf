#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成详细的任务分配表
"""

import pandas as pd
from datetime import datetime, timedelta

def is_workday(date):
    """判断是否为工作日（周一到周五）"""
    return date.weekday() < 5

def get_next_workday(date):
    """获取下一个工作日"""
    next_day = date + timedelta(days=1)
    while not is_workday(next_day):
        next_day += timedelta(days=1)
    return next_day

def main():
    # 用例工时数据
    use_case_hours = [2.5] * 18 + [5] * 6 + [2.5] * 4 + [5] * 6 + [2.5] * 3 + [5] * 1
    
    # 重新优化分配，确保三人同时结束
    # 黄子拓和任海棠：2025-09-15开始，每人约47.5小时（4.75天）
    # 曾奇成：2025-09-17开始，约27.5小时（2.75天）
    # 目标：都在2025-09-19结束

    assignments = {
        '黄子拓': [1, 2, 3, 4, 19, 20, 21, 29, 30, 31, 32],  # 47.5小时
        '任海棠': [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 25],  # 37.5小时
        '曾奇成': [22, 23, 24, 26, 27, 28, 33, 34, 35, 36, 37, 38]  # 42.5小时
    }
    
    start_dates = {
        '黄子拓': datetime(2025, 9, 15),
        '任海棠': datetime(2025, 9, 15),
        '曾奇成': datetime(2025, 9, 17)
    }
    
    # 创建详细的任务表
    task_list = []
    
    for person, case_ids in assignments.items():
        current_date = start_dates[person]
        daily_hours = 0
        
        for case_id in sorted(case_ids):
            case_hours = use_case_hours[case_id - 1]
            
            # 如果当天剩余时间不够，移到下一个工作日
            if daily_hours + case_hours > 10:
                current_date = get_next_workday(current_date)
                daily_hours = 0
            
            # 计算结束时间
            if daily_hours + case_hours <= 10:
                # 当天可以完成
                start_time = current_date
                end_time = current_date
                daily_hours += case_hours
            else:
                # 需要跨天完成
                start_time = current_date
                remaining_hours = case_hours
                temp_date = current_date
                temp_daily_hours = daily_hours
                
                while remaining_hours > 0:
                    available_hours = 10 - temp_daily_hours
                    if remaining_hours <= available_hours:
                        end_time = temp_date
                        daily_hours = temp_daily_hours + remaining_hours
                        remaining_hours = 0
                    else:
                        remaining_hours -= available_hours
                        temp_date = get_next_workday(temp_date)
                        temp_daily_hours = 0
                        if remaining_hours > 0:
                            end_time = temp_date
                            daily_hours = remaining_hours
                            remaining_hours = 0
            
            task_list.append({
                '用例编号': f'用例{case_id:02d}',
                '用例工时': case_hours,
                '责任人': person,
                '开始时间': start_time.strftime('%Y-%m-%d'),
                '结束时间': end_time.strftime('%Y-%m-%d')
            })
            
            # 如果当天工作满10小时，移到下一个工作日
            if daily_hours >= 10:
                current_date = get_next_workday(current_date)
                daily_hours = 0
    
    # 按用例编号排序
    task_list.sort(key=lambda x: int(x['用例编号'][2:]))
    
    # 创建DataFrame
    df = pd.DataFrame(task_list)
    
    # 保存为CSV文件
    df.to_csv('task_allocation.csv', index=False, encoding='utf-8-sig')
    
    # 显示结果
    print("详细任务分配表:")
    print("=" * 60)
    print(df.to_string(index=False))
    
    # 统计信息
    print("\n\n统计信息:")
    print("=" * 40)
    for person in ['黄子拓', '任海棠', '曾奇成']:
        person_tasks = df[df['责任人'] == person]
        total_hours = person_tasks['用例工时'].sum()
        task_count = len(person_tasks)
        start_date = person_tasks['开始时间'].min()
        end_date = person_tasks['结束时间'].max()
        
        print(f"\n{person}:")
        print(f"  任务数量: {task_count} 个")
        print(f"  总工时: {total_hours} 小时")
        print(f"  开始时间: {start_date}")
        print(f"  结束时间: {end_date}")
    
    print(f"\n文件已保存为: task_allocation.csv")

if __name__ == "__main__":
    main()
