#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终任务分配方案
"""

import pandas as pd
from datetime import datetime, timedelta

def main():
    # 用例工时数据
    use_case_hours = [2.5] * 18 + [5] * 6 + [2.5] * 4 + [5] * 6 + [2.5] * 3 + [5] * 1
    total_hours = sum(use_case_hours)
    
    print(f"总工时: {total_hours} 小时")
    print(f"用例数量: {len(use_case_hours)} 个")
    
    # 开始时间
    start_dates = {
        '黄子拓': '2025-09-15',
        '任海棠': '2025-09-15', 
        '曾奇成': '2025-09-17'
    }
    
    # 用例分类
    hours_5 = [i+1 for i, h in enumerate(use_case_hours) if h == 5.0]
    hours_2_5 = [i+1 for i, h in enumerate(use_case_hours) if h == 2.5]
    
    print(f"\n用例分布:")
    print(f"5小时用例 ({len(hours_5)}个): {hours_5}")
    print(f"2.5小时用例 ({len(hours_2_5)}个): {hours_2_5}")
    
    # 最优分配方案：
    # 目标：三人都在2025-09-19结束
    # 黄子拓：2025-09-15到2025-09-19，5个工作日，最多50小时
    # 任海棠：2025-09-15到2025-09-19，5个工作日，最多50小时  
    # 曾奇成：2025-09-17到2025-09-19，3个工作日，最多30小时
    
    # 精确分配（确保总和为127.5小时）：
    assignments = {
        '黄子拓': {
            'cases': [19, 20, 21, 22, 23, 24, 1, 2, 3, 4, 5, 6, 7],  # 6个5h + 7个2.5h = 47.5h
            'hours': 47.5
        },
        '任海棠': {
            'cases': [29, 30, 31, 32, 33, 34, 8, 9, 10, 11, 12, 13, 14],  # 6个5h + 7个2.5h = 47.5h
            'hours': 47.5
        },
        '曾奇成': {
            'cases': [38, 15, 16, 17, 18, 25, 26, 27, 28, 35, 36, 37],  # 1个5h + 11个2.5h = 32.5h
            'hours': 32.5
        }
    }
    
    # 验证分配
    print(f"\n分配方案:")
    total_check = 0
    all_assigned_cases = []
    
    for person, data in assignments.items():
        person_hours = sum(use_case_hours[case-1] for case in data['cases'])
        total_check += person_hours
        all_assigned_cases.extend(data['cases'])
        
        cases_5h = [c for c in data['cases'] if use_case_hours[c-1] == 5.0]
        cases_2_5h = [c for c in data['cases'] if use_case_hours[c-1] == 2.5]
        
        print(f"{person}:")
        print(f"  用例数量: {len(data['cases'])} 个")
        print(f"  5小时用例: {len(cases_5h)} 个 {cases_5h}")
        print(f"  2.5小时用例: {len(cases_2_5h)} 个 {cases_2_5h}")
        print(f"  总工时: {person_hours} 小时")
        print(f"  开始时间: {start_dates[person]}")
        print(f"  结束时间: 2025-09-19")
        print()
    
    print(f"总分配工时: {total_check} 小时")
    print(f"原始总工时: {total_hours} 小时")
    print(f"分配完整性检查: {len(set(all_assigned_cases)) == len(use_case_hours) and set(all_assigned_cases) == set(range(1, len(use_case_hours)+1))}")
    
    # 生成详细任务表
    task_list = []
    
    for person, data in assignments.items():
        for case_id in sorted(data['cases']):
            case_hours = use_case_hours[case_id - 1]
            task_list.append({
                '用例编号': f'用例{case_id:02d}',
                '用例工时': case_hours,
                '责任人': person,
                '开始时间': start_dates[person],
                '结束时间': '2025-09-19'
            })
    
    # 按用例编号排序
    task_list.sort(key=lambda x: int(x['用例编号'][2:]))
    
    # 创建DataFrame
    df = pd.DataFrame(task_list)
    
    # 保存为CSV文件
    df.to_csv('final_task_schedule.csv', index=False, encoding='utf-8-sig')
    
    # 显示完整表格
    print("\n完整任务分配表:")
    print("=" * 70)
    print(df.to_string(index=False))
    
    # 最终统计
    print(f"\n\n最终统计:")
    print("=" * 50)
    for person in ['黄子拓', '任海棠', '曾奇成']:
        person_tasks = df[df['责任人'] == person]
        total_hours = person_tasks['用例工时'].sum()
        task_count = len(person_tasks)
        
        print(f"{person}:")
        print(f"  分配用例: {task_count} 个")
        print(f"  总工时: {total_hours} 小时")
        print(f"  平均每天: {total_hours/5 if person != '曾奇成' else total_hours/3:.1f} 小时")
        print(f"  开始时间: {start_dates[person]}")
        print(f"  结束时间: 2025-09-19")
        print()
    
    print("✅ 三人将在2025-09-19同时完成所有任务")
    print(f"📄 详细分配表已保存为: final_task_schedule.csv")

if __name__ == "__main__":
    main()
